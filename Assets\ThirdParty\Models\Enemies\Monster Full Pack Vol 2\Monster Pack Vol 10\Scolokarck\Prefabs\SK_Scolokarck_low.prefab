%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1001 &1727833975149964647
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 3dbad139329d4eb8abacddcc1127256b,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 8.16372
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 3dbad139329d4eb8abacddcc1127256b,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 3dbad139329d4eb8abacddcc1127256b,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.76967
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 3dbad139329d4eb8abacddcc1127256b,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 3dbad139329d4eb8abacddcc1127256b,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 3dbad139329d4eb8abacddcc1127256b,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 3dbad139329d4eb8abacddcc1127256b,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 3dbad139329d4eb8abacddcc1127256b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 3dbad139329d4eb8abacddcc1127256b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 3dbad139329d4eb8abacddcc1127256b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 3dbad139329d4eb8abacddcc1127256b,
        type: 3}
      propertyPath: m_Name
      value: SK_Scolokarck_low
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: 3dbad139329d4eb8abacddcc1127256b,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 2868642465859804763}
  m_SourcePrefab: {fileID: 100100000, guid: 3dbad139329d4eb8abacddcc1127256b, type: 3}
--- !u!1 &1962186292366610486 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 3dbad139329d4eb8abacddcc1127256b,
    type: 3}
  m_PrefabInstance: {fileID: 1727833975149964647}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &2868642465859804763
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1962186292366610486}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6e861cd728fd49b69e30e17bd76642da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  bakerData:
    animations:
    - animationID: Idle
      animatorStateName: Armature_Idle
      animationType: 0
      singleClipData:
        animationClip: {fileID: 7400000, guid: aa551e9e27d89f84e842f90fc7e6c057, type: 2}
      dualClipBlendData:
        blendParameterName: 
        clip1:
          parameterValue: 0
          animationClip: {fileID: 0}
        clip2:
          parameterValue: 0
          animationClip: {fileID: 0}
        nbrOfInBetweenSamples: 0
      loop: 0
      additionalAnimatorParameterValues: []
      additionalAnimatorStatesPerLayer: []
    - animationID: Walk
      animatorStateName: Armature_Walk
      animationType: 0
      singleClipData:
        animationClip: {fileID: 7400000, guid: a826874874c7920478d515023373299d, type: 2}
      dualClipBlendData:
        blendParameterName: 
        clip1:
          parameterValue: 0
          animationClip: {fileID: 0}
        clip2:
          parameterValue: 0
          animationClip: {fileID: 0}
        nbrOfInBetweenSamples: 0
      loop: 0
      additionalAnimatorParameterValues: []
      additionalAnimatorStatesPerLayer: []
    - animationID: Attack
      animatorStateName: Armature_Attack_01
      animationType: 0
      singleClipData:
        animationClip: {fileID: 7400000, guid: 15206ca8396250f4abd9fb0256abb808, type: 2}
      dualClipBlendData:
        blendParameterName: 
        clip1:
          parameterValue: 0
          animationClip: {fileID: 0}
        clip2:
          parameterValue: 0
          animationClip: {fileID: 0}
        nbrOfInBetweenSamples: 0
      loop: 0
      additionalAnimatorParameterValues: []
      additionalAnimatorStatesPerLayer: []
    - animationID: Hit
      animatorStateName: Armature_Hit_01
      animationType: 0
      singleClipData:
        animationClip: {fileID: 7400000, guid: b3c77778a2b17424b91d7501e4eaea96, type: 2}
      dualClipBlendData:
        blendParameterName: 
        clip1:
          parameterValue: 0
          animationClip: {fileID: 0}
        clip2:
          parameterValue: 0
          animationClip: {fileID: 0}
        nbrOfInBetweenSamples: 0
      loop: 0
      additionalAnimatorParameterValues: []
      additionalAnimatorStatesPerLayer: []
    - animationID: Dead
      animatorStateName: Armature_Death
      animationType: 0
      singleClipData:
        animationClip: {fileID: 7400000, guid: a0ad23364c86f5e4f8a88c349a7ede71, type: 2}
      dualClipBlendData:
        blendParameterName: 
        clip1:
          parameterValue: 0
          animationClip: {fileID: 0}
        clip2:
          parameterValue: 0
          animationClip: {fileID: 0}
        nbrOfInBetweenSamples: 0
      loop: 0
      additionalAnimatorParameterValues: []
      additionalAnimatorStatesPerLayer: []
    generateAnimationIdsEnum: 0
    animationIdsEnumName: 
    usePredefinedAnimationEventIds: 0
    predefinedAnimationEventIds: []
    generateAnimationEventIdsEnum: 0
    animationEventIdsEnumName: 
    attachmentAnchors: []
    generateAttachmentAnchorIdsEnum: 0
    attachmentAnchorIdsEnumName: 
    boneUsage:
      numberOfBonesPerVertex: 6
      boneUsagesPerLoD: []
    transformUsageFlagsParent: 2
    transformUsageFlagsChildren: 1
  gpuEcsAnimator: {fileID: 7169558298283258049, guid: 6d1166979fb7e254abd2ceea218ddf35,
    type: 3}
