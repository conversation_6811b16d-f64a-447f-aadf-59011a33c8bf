﻿using MonoToECSShadow.Components;
using PlayerFAP.Components;
using ProjectDawn.Navigation;
using ProjectDawn.Navigation.Sample.BoardDefense;
using Unity.Burst;
using Unity.Burst.Intrinsics;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;

namespace Systems.PlayerFAP
{
    [BurstCompile]
    [UpdateBefore(typeof(AgentCountSystem))]
    public partial struct EnemyMovementSystem : ISystem
    {
        private EntityQuery m_Query;
        
        private const float tolerance = 0.1f;
        private float3 m_lastPlayerPosition;
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<EnemyTag>();
            // Define the query to select entities with AgentBody and SetDestination components
            m_Query = state.GetEntityQuery(
                ComponentType.ReadWrite<AgentBody>());
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            float deltaTime = SystemAPI.Time.DeltaTime;
            
            float3 playerPosition = float3.zero;
            bool playerFound = false;

            // Query the player position
            foreach (var playerTransform in SystemAPI.Query<RefRO<PlayerTransform>>())
            {
                playerPosition = playerTransform.ValueRO.Position;
                playerFound = true;
                break; // Assuming only one player entity exists
            }

            if (!playerFound) return;
            
            // Compare positions with tolerance
            if (math.distancesq(m_lastPlayerPosition, playerPosition) < tolerance * tolerance)
                return;

            
            // // Move each enemy toward the player
            // foreach (var (localTransform, movementSpeed, enemyTag) in SystemAPI
            //              .Query<RefRW<LocalTransform>, RefRO<EnemyMovementSpeed>, RefRO<EnemyTag>>())
            // {
            //     float3 direction = math.normalize(playerPosition - localTransform.ValueRW.Position);
            //     localTransform.ValueRW.Position += direction * movementSpeed.ValueRO.Value * deltaTime;
            // }
            
            foreach (var body in SystemAPI.Query<RefRW<SetDestination>>())
            {
                body.ValueRW.Value = playerPosition;
            }
            
            // var job = new SetDestinationJob
            // {
            //     AgentBodyTypeHandle = state.GetComponentTypeHandle<AgentBody>(),
            // };
            //
            // // Schedule the job
            // state.Dependency = job.ScheduleParallel(m_Query, state.Dependency);
            //
            
            m_lastPlayerPosition = playerPosition;
        }
    }

    //[BurstCompile]
    [BurstCompile]
    public struct SetDestinationJob : IJobChunk
    {
        public ComponentTypeHandle<AgentBody> AgentBodyTypeHandle;
        [ReadOnly] public ComponentTypeHandle<AgentBody> SetDestinationTypeHandle;

        public float3 playerPosition;
        
        public void Execute(in ArchetypeChunk chunk, int unfilteredChunkIndex, bool useEnabledMask, in v128 chunkEnabledMask)
        {
            var agentBodies = chunk.GetNativeArray(AgentBodyTypeHandle);
            var destinations = chunk.GetNativeArray(SetDestinationTypeHandle);

            for (int i = 0; i < chunk.Count; i++)
            {
                var agentBody = agentBodies[i];
                var destination = destinations[i];

                agentBody.SetDestination(playerPosition);

                // Update the AgentBody component in the chunk
                agentBodies[i] = agentBody;
            }
        }
    }

}