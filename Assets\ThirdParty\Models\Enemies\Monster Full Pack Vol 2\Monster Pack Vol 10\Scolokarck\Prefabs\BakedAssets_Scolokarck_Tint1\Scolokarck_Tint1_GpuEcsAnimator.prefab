%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1152276122630657237
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7687379922076954630}
  - component: {fileID: 6771670946371927709}
  m_Layer: 0
  m_Name: Scolokarck_Tint1_GpuEcsAnimator
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7687379922076954630
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1152276122630657237}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3092044484425323973}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6771670946371927709
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1152276122630657237}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6dbd5b1412c14f19ba40d2188c5cd8c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  totalNbrOfFrames: 198
  nbrOfAttachmentAnchors: 0
  animations:
  - startFrameIndex: 0
    nbrOfFramesPerSample: 25
    nbrOfInBetweenSamples: 1
    blendTimeCorrection: 1
    startEventOccurenceId: 0
    nbrOfEventOccurenceIds: 0
    loop: 1
  - startFrameIndex: 25
    nbrOfFramesPerSample: 61
    nbrOfInBetweenSamples: 1
    blendTimeCorrection: 1
    startEventOccurenceId: 0
    nbrOfEventOccurenceIds: 0
    loop: 1
  - startFrameIndex: 86
    nbrOfFramesPerSample: 31
    nbrOfInBetweenSamples: 1
    blendTimeCorrection: 1
    startEventOccurenceId: 0
    nbrOfEventOccurenceIds: 0
    loop: 1
  - startFrameIndex: 117
    nbrOfFramesPerSample: 26
    nbrOfInBetweenSamples: 1
    blendTimeCorrection: 1
    startEventOccurenceId: 0
    nbrOfEventOccurenceIds: 0
    loop: 1
  - startFrameIndex: 143
    nbrOfFramesPerSample: 55
    nbrOfInBetweenSamples: 1
    blendTimeCorrection: 1
    startEventOccurenceId: 0
    nbrOfEventOccurenceIds: 0
    loop: 0
  animationEventOccurences: []
  transformUsageFlags: 2
  attachmentAnchorData: {fileID: 0}
--- !u!1 &8343634880872429810
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3092044484425323973}
  - component: {fileID: 2616627932339515245}
  - component: {fileID: 6038305440662927311}
  - component: {fileID: 8176616881536723390}
  m_Layer: 0
  m_Name: SK_Scolokarck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3092044484425323973
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8343634880872429810}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 52.131187, y: 52.131187, z: 52.131187}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7687379922076954630}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2616627932339515245
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8343634880872429810}
  m_Mesh: {fileID: 4300000, guid: 3eec9685120d4bc4eb46d0633d0e820e, type: 2}
--- !u!23 &6038305440662927311
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8343634880872429810}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1f6eedd7eca2e8446bfadc14c6ed059e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &8176616881536723390
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8343634880872429810}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b927b9dfd6f248378c3364a809fda80f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  animator: {fileID: 6771670946371927709}
  transformUsageFlags: 1
