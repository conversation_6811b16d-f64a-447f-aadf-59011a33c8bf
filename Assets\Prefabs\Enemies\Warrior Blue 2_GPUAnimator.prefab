%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &698144096656446527
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3255391586631462808}
  - component: {fileID: 6079397691676785622}
  - component: {fileID: 7457356831284561935}
  m_Layer: 0
  m_Name: Tail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3255391586631462808
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 698144096656446527}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.611, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2995525588820971350}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6079397691676785622
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 698144096656446527}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b275e5f92732148048d7b77e264ac30e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShapeType: 1
  m_PrimitiveCenter:
    x: 0
    y: 0
    z: 0
  m_PrimitiveSize:
    x: 0.2
    y: 0.2
    z: 0.1
  m_PrimitiveOrientation:
    Value:
      x: 90
      y: 0
      z: 0
    RotationOrder: 4
  m_Capsule:
    Height: 0.1
    Radius: 0.1
    Axis: 2
  m_Cylinder:
    Height: 0.1
    Radius: 0.1
    Axis: 2
  m_CylinderSideCount: 20
  m_SphereRadius: 0.1
  m_MinimumSkinnedVertexWeight: 0.1
  m_ConvexHullGenerationParameters:
    m_SimplificationTolerance: 0.03341032
    m_BevelRadius: 0.05
    m_MinimumAngle: 2.5000002
  m_CustomMesh: {fileID: 0}
  m_ForceUnique: 0
  m_Material:
    m_SupportsTemplate: 1
    m_Template: {fileID: 0}
    m_CollisionResponse:
      m_Override: 0
      m_Value: 0
    m_Friction:
      m_Override: 0
      m_Value:
        Value: 0.5
        CombineMode: 0
    m_Restitution:
      m_Override: 0
      m_Value:
        Value: 0
        CombineMode: 2
    m_BelongsToCategories:
      m_Override: 0
      m_Value:
        Category00: 0
        Category01: 0
        Category02: 0
        Category03: 0
        Category04: 1
        Category05: 0
        Category06: 0
        Category07: 0
        Category08: 0
        Category09: 0
        Category10: 0
        Category11: 0
        Category12: 0
        Category13: 0
        Category14: 0
        Category15: 0
        Category16: 0
        Category17: 0
        Category18: 0
        Category19: 0
        Category20: 0
        Category21: 0
        Category22: 0
        Category23: 0
        Category24: 0
        Category25: 0
        Category26: 0
        Category27: 0
        Category28: 0
        Category29: 0
        Category30: 0
        Category31: 0
    m_CollidesWithCategories:
      m_Override: 0
      m_Value:
        Category00: 1
        Category01: 0
        Category02: 0
        Category03: 0
        Category04: 0
        Category05: 0
        Category06: 0
        Category07: 0
        Category08: 0
        Category09: 0
        Category10: 0
        Category11: 0
        Category12: 0
        Category13: 0
        Category14: 0
        Category15: 0
        Category16: 0
        Category17: 0
        Category18: 0
        Category19: 0
        Category20: 0
        Category21: 0
        Category22: 0
        Category23: 0
        Category24: 0
        Category25: 0
        Category26: 0
        Category27: 0
        Category28: 0
        Category29: 0
        Category30: 0
        Category31: 0
    m_CustomMaterialTags:
      m_Override: 0
      m_Value:
        Tag00: 0
        Tag01: 0
        Tag02: 0
        Tag03: 0
        Tag04: 0
        Tag05: 0
        Tag06: 0
        Tag07: 0
    m_SerializedVersion: 1
  m_SerializedVersion: 1
--- !u!114 &7457356831284561935
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 698144096656446527}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ccea9ea98e38942e0b0938c27ed1903e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MotionType: 1
  m_Smoothing: 0
  m_Mass: 1
  m_LinearDamping: 0.01
  m_AngularDamping: 0.05
  m_InitialLinearVelocity:
    x: 0
    y: 0
    z: 0
  m_InitialAngularVelocity:
    x: 0
    y: 0
    z: 0
  m_GravityFactor: 1
  m_OverrideDefaultMassDistribution: 0
  m_CenterOfMass:
    x: 0
    y: 0
    z: 0
  m_Orientation:
    Value:
      x: 0
      y: 0
      z: 0
    RotationOrder: 4
  m_InertiaTensor:
    x: 0.4
    y: 0.4
    z: 0.4
  m_WorldIndex: 0
  m_CustomTags:
    Tag00: 0
    Tag01: 0
    Tag02: 0
    Tag03: 0
    Tag04: 0
    Tag05: 0
    Tag06: 0
    Tag07: 0
--- !u!1 &1355275861092370815
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7892080842942240105}
  - component: {fileID: 4597628233612748294}
  - component: {fileID: 7841145333282579785}
  - component: {fileID: 7417395611900549628}
  - component: {fileID: 9201764279625843102}
  - component: {fileID: 6719991428033269080}
  - component: {fileID: 7418152016606058246}
  - component: {fileID: 4654516980086769786}
  - component: {fileID: 7817223800542266624}
  - component: {fileID: 919814729630435707}
  m_Layer: 20
  m_Name: Warrior Blue 2_GPUAnimator
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7892080842942240105
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.5, y: 1.5, z: 1.5}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2720968755411403250}
  - {fileID: 7467733275414856131}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4597628233612748294
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 825ee44755f2e4846a6229365ed8d44c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MotionType: 3
  Speed: 2
  Acceleration: 8
  AngularSpeed: 240
  StoppingDistance: 1
  AutoBreaking: 0
  m_Layers: 1
--- !u!114 &7841145333282579785
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fed5c6dd07c7d6e4f857b420fa8f559a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Radius: 0.45
  Height: 1
--- !u!114 &7417395611900549628
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6d72058503702eb42aca97d680e69de1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Layers: 1
--- !u!114 &9201764279625843102
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ed36afb314b833149b4779d0bfe39738, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Group: {fileID: 0}
--- !u!114 &6719991428033269080
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e451aefa74c549d7abb597fe755b8a92, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stoppingDistance: 2
  m_bodyParts:
  - Transform: {fileID: 6672933306646807023}
    BodyPartType: 0
  - Transform: {fileID: 6567619774804622545}
    BodyPartType: 1
  - Transform: {fileID: 4333022837465081841}
    BodyPartType: 4
  animationSystemType: 0
  rukhankaAnimationMappings:
  - animationType: 0
    clip: {fileID: 7400000, guid: a63c3a0897a11424097bd160fe329c41, type: 2}
    animationName: Idle
  - animationType: 5
    clip: {fileID: 7400000, guid: 0219ea3cb908e7c43876509198e4cb24, type: 2}
    animationName: Hit
  - animationType: 1
    clip: {fileID: 7400000, guid: db7ef3d26d13d61458a4f4aa19afafd6, type: 2}
    animationName: Walk
  - animationType: 3
    clip: {fileID: 7400000, guid: aeaab26cb031ed1489f9ab7e7f605a26, type: 2}
    animationName: Attack
  - animationType: 4
    clip: {fileID: 7400000, guid: aa324ebbf56e0094f8e18ac06d2dbdbf, type: 2}
    animationName: Death
  rukhankaAnimator: {fileID: 0}
  animator: {fileID: 7278604582437144620}
  moveSpeed: 3.5
  animationTransitionSpeed: 0.25
  runThreshold: 2
  m_attackBelongsTo:
    Category00: 0
    Category01: 0
    Category02: 0
    Category03: 0
    Category04: 0
    Category05: 0
    Category06: 0
    Category07: 0
    Category08: 0
    Category09: 0
    Category10: 0
    Category11: 0
    Category12: 0
    Category13: 0
    Category14: 0
    Category15: 0
    Category16: 0
    Category17: 0
    Category18: 0
    Category19: 0
    Category20: 0
    Category21: 0
    Category22: 0
    Category23: 0
    Category24: 0
    Category25: 0
    Category26: 0
    Category27: 0
    Category28: 0
    Category29: 0
    Category30: 0
    Category31: 0
  m_attackCollideWith:
    Category00: 0
    Category01: 0
    Category02: 0
    Category03: 0
    Category04: 0
    Category05: 0
    Category06: 0
    Category07: 0
    Category08: 0
    Category09: 0
    Category10: 0
    Category11: 0
    Category12: 0
    Category13: 0
    Category14: 0
    Category15: 0
    Category16: 0
    Category17: 0
    Category18: 0
    Category19: 0
    Category20: 0
    Category21: 0
    Category22: 0
    Category23: 0
    Category24: 0
    Category25: 0
    Category26: 0
    Category27: 0
    Category28: 0
    Category29: 0
    Category30: 0
    Category31: 0
--- !u!114 &7418152016606058246
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b275e5f92732148048d7b77e264ac30e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShapeType: 1
  m_PrimitiveCenter:
    x: 0
    y: 0.16
    z: 0
  m_PrimitiveSize:
    x: 1.58
    y: 1.58
    z: 0.5
  m_PrimitiveOrientation:
    Value:
      x: 90
      y: 0
      z: 0
    RotationOrder: 4
  m_Capsule:
    Height: 0.5
    Radius: 0.79
    Axis: 2
  m_Cylinder:
    Height: 0.5
    Radius: 0.79
    Axis: 2
  m_CylinderSideCount: 20
  m_SphereRadius: 0.79
  m_MinimumSkinnedVertexWeight: 0.1
  m_ConvexHullGenerationParameters:
    m_SimplificationTolerance: 0.03341032
    m_BevelRadius: 0.05
    m_MinimumAngle: 2.5000002
  m_CustomMesh: {fileID: 0}
  m_ForceUnique: 0
  m_Material:
    m_SupportsTemplate: 1
    m_Template: {fileID: 0}
    m_CollisionResponse:
      m_Override: 0
      m_Value: 0
    m_Friction:
      m_Override: 0
      m_Value:
        Value: 0
        CombineMode: 0
    m_Restitution:
      m_Override: 0
      m_Value:
        Value: 0
        CombineMode: 2
    m_BelongsToCategories:
      m_Override: 0
      m_Value:
        Category00: 0
        Category01: 0
        Category02: 0
        Category03: 0
        Category04: 1
        Category05: 0
        Category06: 0
        Category07: 0
        Category08: 0
        Category09: 0
        Category10: 0
        Category11: 0
        Category12: 0
        Category13: 0
        Category14: 0
        Category15: 0
        Category16: 0
        Category17: 0
        Category18: 0
        Category19: 0
        Category20: 0
        Category21: 0
        Category22: 0
        Category23: 0
        Category24: 0
        Category25: 0
        Category26: 0
        Category27: 0
        Category28: 0
        Category29: 0
        Category30: 0
        Category31: 0
    m_CollidesWithCategories:
      m_Override: 0
      m_Value:
        Category00: 0
        Category01: 0
        Category02: 0
        Category03: 1
        Category04: 0
        Category05: 0
        Category06: 0
        Category07: 0
        Category08: 0
        Category09: 0
        Category10: 0
        Category11: 0
        Category12: 0
        Category13: 0
        Category14: 0
        Category15: 0
        Category16: 0
        Category17: 0
        Category18: 0
        Category19: 0
        Category20: 0
        Category21: 0
        Category22: 0
        Category23: 0
        Category24: 0
        Category25: 0
        Category26: 0
        Category27: 0
        Category28: 0
        Category29: 0
        Category30: 0
        Category31: 0
    m_CustomMaterialTags:
      m_Override: 0
      m_Value:
        Tag00: 0
        Tag01: 0
        Tag02: 0
        Tag03: 0
        Tag04: 0
        Tag05: 0
        Tag06: 0
        Tag07: 0
    m_SerializedVersion: 1
  m_SerializedVersion: 1
--- !u!114 &4654516980086769786
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ccea9ea98e38942e0b0938c27ed1903e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MotionType: 1
  m_Smoothing: 0
  m_Mass: 1
  m_LinearDamping: 0.01
  m_AngularDamping: 0.05
  m_InitialLinearVelocity:
    x: 0
    y: 0
    z: 0
  m_InitialAngularVelocity:
    x: 0
    y: 0
    z: 0
  m_GravityFactor: 1
  m_OverrideDefaultMassDistribution: 0
  m_CenterOfMass:
    x: 0
    y: 0
    z: 0
  m_Orientation:
    Value:
      x: 0
      y: 0
      z: 0
    RotationOrder: 4
  m_InertiaTensor:
    x: 0.4
    y: 0.4
    z: 0.4
  m_WorldIndex: 0
  m_CustomTags:
    Tag00: 0
    Tag01: 0
    Tag02: 0
    Tag03: 0
    Tag04: 0
    Tag05: 0
    Tag06: 0
    Tag07: 0
--- !u!114 &7817223800542266624
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ff78a121c1d7684449d1ff362a65cbdb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  healthBarPrefab: {fileID: 2517986949255069691, guid: 2ff32cd8c824ebc479fdc0c9b8cc2b84,
    type: 3}
  maxHealth: 100
  Priority: 0
  uiOffsetTransform: {fileID: 2720968755411403250}
--- !u!114 &919814729630435707
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 42a4cd4cbf6cf044887ce5ff1955fa9e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HiveMindStop:
    Enabled: 1
    Radius: 1
  m_GiveUpStop:
    Enabled: 1
    FatigueSpeed: 1
    RecoverySpeed: 0.4
--- !u!1 &1665498472895263286
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2995525588820971350}
  m_Layer: 0
  m_Name: AttackCollider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2995525588820971350
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1665498472895263286}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.00442, z: -0}
  m_LocalScale: {x: 0.02, y: 0.02, z: 0.02}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4112186781728858062}
  - {fileID: 3255391586631462808}
  m_Father: {fileID: 7467733275414856131}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1891555024040099284
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4333022837465081841}
  m_Layer: 20
  m_Name: Hip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4333022837465081841
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1891555024040099284}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.027, z: -0.483}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4714542415424470606}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2102848169767375112
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6567619774804622545}
  m_Layer: 20
  m_Name: Chest
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6567619774804622545
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2102848169767375112}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.071, z: 0.095}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4714542415424470606}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2350047037346264346
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4112186781728858062}
  - component: {fileID: 2040091645926307}
  - component: {fileID: 4448370223500263066}
  m_Layer: 0
  m_Name: Mouth
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4112186781728858062
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2350047037346264346}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2995525588820971350}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2040091645926307
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2350047037346264346}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b275e5f92732148048d7b77e264ac30e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShapeType: 1
  m_PrimitiveCenter:
    x: 0
    y: 0
    z: 0
  m_PrimitiveSize:
    x: 0.2
    y: 0.2
    z: 0.1
  m_PrimitiveOrientation:
    Value:
      x: 90
      y: 0
      z: 0
    RotationOrder: 4
  m_Capsule:
    Height: 0.1
    Radius: 0.1
    Axis: 2
  m_Cylinder:
    Height: 0.1
    Radius: 0.1
    Axis: 2
  m_CylinderSideCount: 20
  m_SphereRadius: 0.1
  m_MinimumSkinnedVertexWeight: 0.1
  m_ConvexHullGenerationParameters:
    m_SimplificationTolerance: 0.03341032
    m_BevelRadius: 0.05
    m_MinimumAngle: 2.5000002
  m_CustomMesh: {fileID: 0}
  m_ForceUnique: 0
  m_Material:
    m_SupportsTemplate: 1
    m_Template: {fileID: 0}
    m_CollisionResponse:
      m_Override: 0
      m_Value: 0
    m_Friction:
      m_Override: 0
      m_Value:
        Value: 0.5
        CombineMode: 0
    m_Restitution:
      m_Override: 0
      m_Value:
        Value: 0
        CombineMode: 2
    m_BelongsToCategories:
      m_Override: 0
      m_Value:
        Category00: 0
        Category01: 0
        Category02: 0
        Category03: 0
        Category04: 1
        Category05: 0
        Category06: 0
        Category07: 0
        Category08: 0
        Category09: 0
        Category10: 0
        Category11: 0
        Category12: 0
        Category13: 0
        Category14: 0
        Category15: 0
        Category16: 0
        Category17: 0
        Category18: 0
        Category19: 0
        Category20: 0
        Category21: 0
        Category22: 0
        Category23: 0
        Category24: 0
        Category25: 0
        Category26: 0
        Category27: 0
        Category28: 0
        Category29: 0
        Category30: 0
        Category31: 0
    m_CollidesWithCategories:
      m_Override: 0
      m_Value:
        Category00: 1
        Category01: 0
        Category02: 0
        Category03: 0
        Category04: 0
        Category05: 0
        Category06: 0
        Category07: 0
        Category08: 0
        Category09: 0
        Category10: 0
        Category11: 0
        Category12: 0
        Category13: 0
        Category14: 0
        Category15: 0
        Category16: 0
        Category17: 0
        Category18: 0
        Category19: 0
        Category20: 0
        Category21: 0
        Category22: 0
        Category23: 0
        Category24: 0
        Category25: 0
        Category26: 0
        Category27: 0
        Category28: 0
        Category29: 0
        Category30: 0
        Category31: 0
    m_CustomMaterialTags:
      m_Override: 0
      m_Value:
        Tag00: 0
        Tag01: 0
        Tag02: 0
        Tag03: 0
        Tag04: 0
        Tag05: 0
        Tag06: 0
        Tag07: 0
    m_SerializedVersion: 1
  m_SerializedVersion: 1
--- !u!114 &4448370223500263066
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2350047037346264346}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ccea9ea98e38942e0b0938c27ed1903e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MotionType: 1
  m_Smoothing: 0
  m_Mass: 1
  m_LinearDamping: 0.01
  m_AngularDamping: 0.05
  m_InitialLinearVelocity:
    x: 0
    y: 0
    z: 0
  m_InitialAngularVelocity:
    x: 0
    y: 0
    z: 0
  m_GravityFactor: 1
  m_OverrideDefaultMassDistribution: 0
  m_CenterOfMass:
    x: 0
    y: 0
    z: 0
  m_Orientation:
    Value:
      x: 0
      y: 0
      z: 0
    RotationOrder: 4
  m_InertiaTensor:
    x: 0.4
    y: 0.4
    z: 0.4
  m_WorldIndex: 0
  m_CustomTags:
    Tag00: 0
    Tag01: 0
    Tag02: 0
    Tag03: 0
    Tag04: 0
    Tag05: 0
    Tag06: 0
    Tag07: 0
--- !u!1 &2715355601175971104
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2720968755411403250}
  m_Layer: 0
  m_Name: UI Offset
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2720968755411403250
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2715355601175971104}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.63, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7892080842942240105}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3196813993678004882
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4714542415424470606}
  m_Layer: 20
  m_Name: Bodypart
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4714542415424470606
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3196813993678004882}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6672933306646807023}
  - {fileID: 6567619774804622545}
  - {fileID: 4333022837465081841}
  m_Father: {fileID: 7467733275414856131}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6598845290936442694
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6672933306646807023}
  m_Layer: 20
  m_Name: Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6672933306646807023
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6598845290936442694}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.171, z: 0.293}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4714542415424470606}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &500543463225931628
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7892080842942240105}
    m_Modifications:
    - target: {fileID: 634940617889288688, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 634940617889288688, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 634940617889288688, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 634940617889288688, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 634940617889288688, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 634940617889288688, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 634940617889288688, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 634940617889288688, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 634940617889288688, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 634940617889288688, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3274890229175632793, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      propertyPath: m_Name
      value: SKM_Creature_Mutant_GpuEcsAnimator
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 7012384418116286127, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 4714542415424470606}
    - targetCorrespondingSourceObject: {fileID: 7012384418116286127, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 2995525588820971350}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: c9bf8a3eca4cf574fb73ec7426bdd493, type: 3}
--- !u!114 &7278604582437144620 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7201403058940280640, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
    type: 3}
  m_PrefabInstance: {fileID: 500543463225931628}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6dbd5b1412c14f19ba40d2188c5cd8c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &7467733275414856131 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7012384418116286127, guid: c9bf8a3eca4cf574fb73ec7426bdd493,
    type: 3}
  m_PrefabInstance: {fileID: 500543463225931628}
  m_PrefabAsset: {fileID: 0}
