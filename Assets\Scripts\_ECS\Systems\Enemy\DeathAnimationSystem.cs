using GPUAnimationCrowds;
using GPUECSAnimationBaker.Engine.AnimatorSystem;
using PlayerFAP.Authorings;
using PlayerFAP.Components;
using PlayerFAP.Systems.Health;
using PlayerFAP.Systems.UI;
using PlayerFAP.Tags;
using ProjectDawn.Navigation;
using Unity.Entities;
using Unity.Physics;
using UnityEngine;

namespace Systems.PlayerFAP
{
    // This system handles death-related tasks for enemies (not animation - that's handled by EnemyAnimationSystem)
    [UpdateAfter(typeof(DamageSystem))]
    [UpdateBefore(typeof(EnemyAnimationSystem))]
    public partial class DeathAnimationSystem : SystemBase
    {
        private EntityCommandBufferSystem m_EndSimulationEcbSystem;

        protected override void OnCreate()
        {
            m_EndSimulationEcbSystem = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>();
            RequireForUpdate<DeadTag>();
        }

        protected override void OnUpdate()
        {
            var ecb = m_EndSimulationEcbSystem.CreateCommandBuffer();

            // Handle newly dead entities - mark them for death animation
            Entities
                .WithNone<DeathAnimationStartedTag>()
                .WithAll<DeadTag>()
                .ForEach((Entity entity,
                         ref CharacterMovementState movementState,
                         in CharacterAnimatorReference animatorRef) =>
                {
                    // Skip if animator entity is invalid
                    if (animatorRef.AnimatorEntity == Entity.Null)
                        return;

                    // Update the movement state to indicate this entity is dead
                    // The EnemyAnimationSystem will handle setting the actual animation
                    movementState.CurrentAnimationID = (int)EnemyAnimationIDs.Dead;
                    movementState.IsMoving = false;
                    movementState.IsAttacking = false;

                    // Add tag to mark that death animation has started
                    ecb.AddComponent<DeathAnimationStartedTag>(entity);
                    DebugLogManager.Instance.Log($"Entity {entity.Index} marked for death animation", DebugLogSettings.LogType.EnemyAnimation);

                    // Hide health bar if it exists
                    if (SystemAPI.HasComponent<HealthBarLink>(entity))
                    {
                        var healthBarLink = SystemAPI.GetComponent<HealthBarLink>(entity);
                        if (healthBarLink.HealthBarEntity != Entity.Null)
                        {
                            // Add a tag to hide the health bar
                            ecb.AddComponent<HideHealthBarTag>(healthBarLink.HealthBarEntity);
                            DebugLogManager.Instance.Log($"Entity {entity.Index} health bar hidden", DebugLogSettings.LogType.EnemyAnimation);
                        }
                    }

                    // Disable physics collider if it exists
                    if (SystemAPI.HasComponent<PhysicsCollider>(entity))
                    {
                        // We can't directly disable a collider, but we can remove it
                        ecb.RemoveComponent<PhysicsCollider>(entity);
                        DebugLogManager.Instance.Log($"Entity {entity.Index} collider disabled", DebugLogSettings.LogType.EnemyAnimation);
                    }

                    // Remove DetectedTag if it exists to prevent the player from targeting this enemy
                    if (SystemAPI.HasComponent<DetectedTag>(entity))
                    {
                        ecb.RemoveComponent<DetectedTag>(entity);
                        DebugLogManager.Instance.Log($"Entity {entity.Index} DetectedTag removed", DebugLogSettings.LogType.EnemyAnimation);
                    }

                    // Remove InFOVTag if it exists
                    if (SystemAPI.HasComponent<InFOVTag>(entity))
                    {
                        ecb.RemoveComponent<InFOVTag>(entity);
                        DebugLogManager.Instance.Log($"Entity {entity.Index} InFOVTag removed", DebugLogSettings.LogType.EnemyAnimation);
                    }

                    // Remove CurrentTargetTag if it exists
                    if (SystemAPI.HasComponent<CurrentTargetTag>(entity))
                    {
                        ecb.RemoveComponent<CurrentTargetTag>(entity);
                        DebugLogManager.Instance.Log($"Entity {entity.Index} CurrentTargetTag removed", DebugLogSettings.LogType.EnemyAnimation);
                    }

                    // Add a component to make the entity undetectable
                    ecb.AddComponent<UndetectableTag>(entity);
                    DebugLogManager.Instance.Log($"Entity {entity.Index} marked as undetectable", DebugLogSettings.LogType.EnemyAnimation);

                    // Stop the agent from moving
                    if (SystemAPI.HasComponent<AgentBody>(entity))
                    {
                        var agentBody = SystemAPI.GetComponent<AgentBody>(entity);
                        agentBody.IsStopped = true;
                        SystemAPI.SetComponent(entity, agentBody);
                        DebugLogManager.Instance.Log($"Entity {entity.Index} agent stopped from moving", DebugLogSettings.LogType.EnemyAnimation);
                    }

                    // Remove SetDestination component to prevent the agent from getting new destinations
                    if (SystemAPI.HasComponent<SetDestination>(entity))
                    {
                        ecb.RemoveComponent<SetDestination>(entity);
                        DebugLogManager.Instance.Log($"Entity {entity.Index} SetDestination component removed", DebugLogSettings.LogType.EnemyAnimation);
                    }

                    DebugLogManager.Instance.Log($"Entity {entity.Index} prepared for death animation", DebugLogSettings.LogType.EnemyAnimation);
                }).WithoutBurst().Run();

            m_EndSimulationEcbSystem.AddJobHandleForProducer(Dependency);
        }
    }

    // Tag to mark that death animation has started
    public struct DeathAnimationStartedTag : IComponentData
    {
    }
}
