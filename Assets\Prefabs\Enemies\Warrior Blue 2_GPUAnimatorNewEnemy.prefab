%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1355275861092370815
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7892080842942240105}
  - component: {fileID: 4597628233612748294}
  - component: {fileID: 7841145333282579785}
  - component: {fileID: 7417395611900549628}
  - component: {fileID: 9201764279625843102}
  - component: {fileID: 6719991428033269080}
  - component: {fileID: 7418152016606058246}
  - component: {fileID: 4654516980086769786}
  - component: {fileID: 7817223800542266624}
  - component: {fileID: 8772788870636837867}
  m_Layer: 20
  m_Name: Warrior Blue 2_GPUAnimatorNewEnemy
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7892080842942240105
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.5, y: 1.5, z: 1.5}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2720968755411403250}
  - {fileID: 5866719740234333450}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4597628233612748294
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 825ee44755f2e4846a6229365ed8d44c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MotionType: 3
  Speed: 2
  Acceleration: 8
  AngularSpeed: 240
  StoppingDistance: 2
  AutoBreaking: 0
  m_Layers: 1
--- !u!114 &7841145333282579785
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fed5c6dd07c7d6e4f857b420fa8f559a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Radius: 0.5
  Height: 1
--- !u!114 &7417395611900549628
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6d72058503702eb42aca97d680e69de1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Layers: -1
--- !u!114 &9201764279625843102
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ed36afb314b833149b4779d0bfe39738, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Group: {fileID: 0}
--- !u!114 &6719991428033269080
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e451aefa74c549d7abb597fe755b8a92, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  stoppingDistance: 2.25
  m_bodyParts:
  - Transform: {fileID: 7040856770156613734}
    BodyPartType: 0
  - Transform: {fileID: 9137162763473601344}
    BodyPartType: 1
  - Transform: {fileID: 4377124146822787244}
    BodyPartType: 4
  animationSystemType: 0
  rukhankaAnimationMappings:
  - animationType: 0
    clip: {fileID: 7400000, guid: aa551e9e27d89f84e842f90fc7e6c057, type: 2}
    animationName: Idle
  - animationType: 1
    clip: {fileID: 7400000, guid: a826874874c7920478d515023373299d, type: 2}
    animationName: Walk
  - animationType: 3
    clip: {fileID: 7400000, guid: 15206ca8396250f4abd9fb0256abb808, type: 2}
    animationName: Attack
  - animationType: 5
    clip: {fileID: 7400000, guid: b3c77778a2b17424b91d7501e4eaea96, type: 2}
    animationName: Hi
  - animationType: 4
    clip: {fileID: 7400000, guid: a0ad23364c86f5e4f8a88c349a7ede71, type: 2}
    animationName: Death
  rukhankaAnimator: {fileID: 0}
  animator: {fileID: 7366770868857959313}
  moveSpeed: 3.5
  animationTransitionSpeed: 0.25
  runThreshold: 2
  m_attackBelongsTo:
    Category00: 0
    Category01: 0
    Category02: 0
    Category03: 0
    Category04: 0
    Category05: 0
    Category06: 0
    Category07: 0
    Category08: 0
    Category09: 0
    Category10: 0
    Category11: 0
    Category12: 0
    Category13: 0
    Category14: 0
    Category15: 0
    Category16: 0
    Category17: 0
    Category18: 0
    Category19: 0
    Category20: 0
    Category21: 0
    Category22: 0
    Category23: 0
    Category24: 0
    Category25: 0
    Category26: 0
    Category27: 0
    Category28: 0
    Category29: 0
    Category30: 0
    Category31: 0
  m_attackCollideWith:
    Category00: 0
    Category01: 0
    Category02: 0
    Category03: 0
    Category04: 0
    Category05: 0
    Category06: 0
    Category07: 0
    Category08: 0
    Category09: 0
    Category10: 0
    Category11: 0
    Category12: 0
    Category13: 0
    Category14: 0
    Category15: 0
    Category16: 0
    Category17: 0
    Category18: 0
    Category19: 0
    Category20: 0
    Category21: 0
    Category22: 0
    Category23: 0
    Category24: 0
    Category25: 0
    Category26: 0
    Category27: 0
    Category28: 0
    Category29: 0
    Category30: 0
    Category31: 0
--- !u!114 &7418152016606058246
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b275e5f92732148048d7b77e264ac30e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShapeType: 1
  m_PrimitiveCenter:
    x: 0
    y: 0.16
    z: 0
  m_PrimitiveSize:
    x: 1.58
    y: 1.58
    z: 0.5
  m_PrimitiveOrientation:
    Value:
      x: 90
      y: 0
      z: 0
    RotationOrder: 4
  m_Capsule:
    Height: 0.5
    Radius: 0.79
    Axis: 2
  m_Cylinder:
    Height: 0.5
    Radius: 0.79
    Axis: 2
  m_CylinderSideCount: 20
  m_SphereRadius: 0.79
  m_MinimumSkinnedVertexWeight: 0.1
  m_ConvexHullGenerationParameters:
    m_SimplificationTolerance: 0.03341032
    m_BevelRadius: 0.05
    m_MinimumAngle: 2.5000002
  m_CustomMesh: {fileID: 0}
  m_ForceUnique: 0
  m_Material:
    m_SupportsTemplate: 1
    m_Template: {fileID: 0}
    m_CollisionResponse:
      m_Override: 0
      m_Value: 0
    m_Friction:
      m_Override: 0
      m_Value:
        Value: 0
        CombineMode: 0
    m_Restitution:
      m_Override: 0
      m_Value:
        Value: 0
        CombineMode: 2
    m_BelongsToCategories:
      m_Override: 0
      m_Value:
        Category00: 0
        Category01: 0
        Category02: 0
        Category03: 0
        Category04: 1
        Category05: 0
        Category06: 0
        Category07: 0
        Category08: 0
        Category09: 0
        Category10: 0
        Category11: 0
        Category12: 0
        Category13: 0
        Category14: 0
        Category15: 0
        Category16: 0
        Category17: 0
        Category18: 0
        Category19: 0
        Category20: 0
        Category21: 0
        Category22: 0
        Category23: 0
        Category24: 0
        Category25: 0
        Category26: 0
        Category27: 0
        Category28: 0
        Category29: 0
        Category30: 0
        Category31: 0
    m_CollidesWithCategories:
      m_Override: 0
      m_Value:
        Category00: 0
        Category01: 0
        Category02: 0
        Category03: 1
        Category04: 0
        Category05: 0
        Category06: 0
        Category07: 0
        Category08: 0
        Category09: 0
        Category10: 0
        Category11: 0
        Category12: 0
        Category13: 0
        Category14: 0
        Category15: 0
        Category16: 0
        Category17: 0
        Category18: 0
        Category19: 0
        Category20: 0
        Category21: 0
        Category22: 0
        Category23: 0
        Category24: 0
        Category25: 0
        Category26: 0
        Category27: 0
        Category28: 0
        Category29: 0
        Category30: 0
        Category31: 0
    m_CustomMaterialTags:
      m_Override: 0
      m_Value:
        Tag00: 0
        Tag01: 0
        Tag02: 0
        Tag03: 0
        Tag04: 0
        Tag05: 0
        Tag06: 0
        Tag07: 0
    m_SerializedVersion: 1
  m_SerializedVersion: 1
--- !u!114 &4654516980086769786
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ccea9ea98e38942e0b0938c27ed1903e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MotionType: 1
  m_Smoothing: 0
  m_Mass: 1
  m_LinearDamping: 0.01
  m_AngularDamping: 0.05
  m_InitialLinearVelocity:
    x: 0
    y: 0
    z: 0
  m_InitialAngularVelocity:
    x: 0
    y: 0
    z: 0
  m_GravityFactor: 1
  m_OverrideDefaultMassDistribution: 0
  m_CenterOfMass:
    x: 0
    y: 0
    z: 0
  m_Orientation:
    Value:
      x: 0
      y: 0
      z: 0
    RotationOrder: 4
  m_InertiaTensor:
    x: 0.4
    y: 0.4
    z: 0.4
  m_WorldIndex: 0
  m_CustomTags:
    Tag00: 0
    Tag01: 0
    Tag02: 0
    Tag03: 0
    Tag04: 0
    Tag05: 0
    Tag06: 0
    Tag07: 0
--- !u!114 &7817223800542266624
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ff78a121c1d7684449d1ff362a65cbdb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  healthBarPrefab: {fileID: 2517986949255069691, guid: 2ff32cd8c824ebc479fdc0c9b8cc2b84,
    type: 3}
  maxHealth: 100
  Priority: 0
  uiOffsetTransform: {fileID: 2720968755411403250}
--- !u!114 &8772788870636837867
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355275861092370815}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 42a4cd4cbf6cf044887ce5ff1955fa9e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HiveMindStop:
    Enabled: 1
    Radius: 0.35
  m_GiveUpStop:
    Enabled: 0
    FatigueSpeed: 1
    RecoverySpeed: 1
--- !u!1 &2513634352427494035
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4377124146822787244}
  m_Layer: 20
  m_Name: Hip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4377124146822787244
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2513634352427494035}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.605, z: -0.483}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5399043000065291070}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2715355601175971104
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2720968755411403250}
  m_Layer: 0
  m_Name: UI Offset
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2720968755411403250
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2715355601175971104}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.63, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7892080842942240105}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3911049236405316695
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5399043000065291070}
  m_Layer: 20
  m_Name: Bodypart
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5399043000065291070
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3911049236405316695}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.111046664, y: 0, z: -0.10001334}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7040856770156613734}
  - {fileID: 9137162763473601344}
  - {fileID: 4377124146822787244}
  m_Father: {fileID: 5866719740234333450}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5837005011144644994
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7040856770156613734}
  m_Layer: 20
  m_Name: Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7040856770156613734
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5837005011144644994}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.41, z: 0.824}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5399043000065291070}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8351450555067164799
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9137162763473601344}
  m_Layer: 20
  m_Name: Chest
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9137162763473601344
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8351450555067164799}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.484, z: 0.095}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5399043000065291070}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &4307070488165584140
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7892080842942240105}
    m_Modifications:
    - target: {fileID: 4220711485441886592, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4220711485441886592, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4220711485441886592, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4220711485441886592, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4220711485441886592, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4220711485441886592, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4220711485441886592, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4220711485441886592, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4220711485441886592, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4220711485441886592, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5511367189351087124, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: e3fbb13a94e8d8b40bc5093d69669a3f, type: 2}
    - target: {fileID: 6771670946371927709, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: animationEventOccurences.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8952758762315678626, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      propertyPath: m_Name
      value: Scolokarck_Tint1_GpuEcsAnimator
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 7687379922076954630, guid: b8851219273d3a94a9bc59bd403b7197,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 5399043000065291070}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: b8851219273d3a94a9bc59bd403b7197, type: 3}
--- !u!4 &5866719740234333450 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7687379922076954630, guid: b8851219273d3a94a9bc59bd403b7197,
    type: 3}
  m_PrefabInstance: {fileID: 4307070488165584140}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &7366770868857959313 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 6771670946371927709, guid: b8851219273d3a94a9bc59bd403b7197,
    type: 3}
  m_PrefabInstance: {fileID: 4307070488165584140}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6dbd5b1412c14f19ba40d2188c5cd8c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
