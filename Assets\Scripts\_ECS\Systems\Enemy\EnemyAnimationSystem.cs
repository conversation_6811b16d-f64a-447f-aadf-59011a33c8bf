using GPUAnimationCrowds;
using GPUECSAnimationBaker.Engine.AnimatorSystem;
using PlayerFAP.Authorings;
using PlayerFAP.Components;
using PlayerFAP.Systems;
using ProjectDawn.Navigation;
using ProjectDawn.Navigation.Sample.BoardDefense;
using Unity.Burst;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using UnityEngine;

namespace Systems.PlayerFAP
{
    // This system updates enemy animations based on movement state
    [UpdateBefore(typeof(GpuEcsAnimatorSystem))]
    [UpdateAfter(typeof(DeathAnimationSystem))]
    public partial struct EnemyAnimationSystem : ISystem
    {
        // Lookup for accessing animator control components
        private ComponentLookup<GpuEcsAnimatorControlComponent> m_AnimatorControlLookup;
        private ComponentLookup<GpuEcsAnimatorControlStateComponent> m_AnimatorControlStateLookup; // For setting animation state
        private ComponentLookup<GpuEcsAnimatorStateComponent> m_AnimatorStateLookup; // Read-only
        private ComponentLookup<GpuEcsAnimationDataComponent> m_AnimationDataLookup; // Read-only

        // For debugging
        private float m_LogTimer;
        private const float LOG_INTERVAL = 2.0f; // Log every 2 seconds to avoid spam

        public void OnCreate(ref SystemState state)
        {
            // Initialize the component lookups
            m_AnimatorControlLookup = state.GetComponentLookup<GpuEcsAnimatorControlComponent>(false); // false = read-write
            m_AnimatorControlStateLookup = state.GetComponentLookup<GpuEcsAnimatorControlStateComponent>(false); // false = read-write
            m_AnimatorStateLookup = state.GetComponentLookup<GpuEcsAnimatorStateComponent>(true); // true = read-only
            m_AnimationDataLookup = state.GetComponentLookup<GpuEcsAnimationDataComponent>(true); // true = read-only

            // Require components for the system to run
            state.RequireForUpdate<CharacterMovementState>();
            state.RequireForUpdate<EnemyTag>();

            // Initialize log timer
            m_LogTimer = 0;

            DebugLogManager.Instance.Log("[EnemyAnimationSystem] System created", DebugLogSettings.LogType.EnemyAnimation);
        }

        public void OnUpdate(ref SystemState state)
        {
            // Update the component lookups
            m_AnimatorControlLookup.Update(ref state);
            m_AnimatorStateLookup.Update(ref state);
            m_AnimationDataLookup.Update(ref state);
            m_AnimatorControlStateLookup.Update(ref state);

            // Update log timer
            m_LogTimer += state.WorldUnmanaged.Time.DeltaTime;
            bool shouldLog = m_LogTimer >= LOG_INTERVAL;
            if (shouldLog)
            {
                m_LogTimer = 0;
                DebugLogManager.Instance.Log("[EnemyAnimationSystem] Updating animations", DebugLogSettings.LogType.EnemyAnimation);

                // Count entities with animation components
                int enemyCount = 0;
                int animatorCount = 0;
                int attackingCount = 0;

                foreach (var (movementState, settings, animatorRef, enemyTag) in
                         SystemAPI.Query<RefRO<CharacterMovementState>, RefRO<CharacterSettings>,
                                         RefRO<CharacterAnimatorReference>, RefRO<EnemyTag>>().WithNone<HitTag>())
                {
                    enemyCount++;
                    if (animatorRef.ValueRO.AnimatorEntity != Entity.Null)
                    {
                        animatorCount++;
                        if (movementState.ValueRO.IsAttacking)
                        {
                            attackingCount++;
                        }
                    }
                }

                DebugLogManager.Instance.Log($"[EnemyAnimationSystem] Found {enemyCount} enemies, {animatorCount} with valid animators, {attackingCount} attacking", DebugLogSettings.LogType.EnemyAnimation);
            }

            // First, handle dead entities separately to avoid structural changes in the job
            foreach (var (movementState, animatorRef, deadTag, entity) in
                     SystemAPI.Query<RefRW<CharacterMovementState>, RefRO<CharacterAnimatorReference>, RefRO<DeadTag>>().WithNone<HitTag>().WithEntityAccess())
            {
                if (animatorRef.ValueRO.AnimatorEntity == Entity.Null)
                    continue;

                if (SystemAPI.HasComponent<HitTag>(entity) || SystemAPI.HasComponent<HitAnimationStartedTag>(entity))
                    return;


                // Always force the dead animation for dead entities
                // Update the movement state
                movementState.ValueRW.CurrentAnimationID = (int)EnemyAnimationIDs.Dead;
                movementState.ValueRW.IsMoving = false;
                movementState.ValueRW.IsAttacking = false;

                if (shouldLog)
                {
                    DebugLogManager.Instance.Log($"[EnemyAnimationSystem] Entity {entity.Index} is dead, ensuring Dead animation is set", DebugLogSettings.LogType.EnemyAnimation);
                }

                // Get the animator control component
                if (m_AnimatorControlLookup.HasComponent(animatorRef.ValueRO.AnimatorEntity))
                {
                    var animatorControl = m_AnimatorControlLookup[animatorRef.ValueRO.AnimatorEntity];

                    // Update the animator control component
                    animatorControl.animatorInfo.animationID = (int)EnemyAnimationIDs.Dead;
                    animatorControl.animatorInfo.blendFactor = 0f;
                    animatorControl.animatorInfo.speedFactor = 1f;
                    animatorControl.transitionSpeed = 0.05f; // Very quick transition to death animation

                    // Update the component
                    m_AnimatorControlLookup[animatorRef.ValueRO.AnimatorEntity] = animatorControl;

                    // Set the animator control state to Start to trigger the animation change
                    if (m_AnimatorControlStateLookup.HasComponent(animatorRef.ValueRO.AnimatorEntity))
                    {
                        var controlState = new GpuEcsAnimatorControlStateComponent
                        {
                            state = GpuEcsAnimatorControlStates.Start
                        };
                        m_AnimatorControlStateLookup[animatorRef.ValueRO.AnimatorEntity] = controlState;

                        if (shouldLog)
                        {
                            DebugLogManager.Instance.Log($"[EnemyAnimationSystem] Entity {entity.Index} animation set to Dead (ID: {(int)EnemyAnimationIDs.Dead})", DebugLogSettings.LogType.EnemyAnimation);
                        }
                    }
                }
            }

            // Create a new job to update animations for non-dead entities
            var updateAnimationsJob = new UpdateAnimationsJob
            {
                AnimatorControlLookup = m_AnimatorControlLookup,
                AnimatorStateLookup = m_AnimatorStateLookup,
                AnimationDataLookup = m_AnimationDataLookup,
                DeadTagLookup = SystemAPI.GetComponentLookup<DeadTag>(true),
                HitTagLookup = SystemAPI.GetComponentLookup<HitTag>(true),
                HitAnimationStartedTagLookup = SystemAPI.GetComponentLookup<HitAnimationStartedTag>(true),
                DeltaTime = state.WorldUnmanaged.Time.DeltaTime,
                ElapsedTime = (float)state.WorldUnmanaged.Time.ElapsedTime,
                ShouldLog = shouldLog
            };

            // Schedule the job with the current dependency
            state.Dependency = updateAnimationsJob.Schedule(state.Dependency);
        }

        // Job to update animations based on movement state
        private partial struct UpdateAnimationsJob : IJobEntity
        {
            // Component lookups for accessing animator components
            public ComponentLookup<GpuEcsAnimatorControlComponent> AnimatorControlLookup;
            [Unity.Collections.ReadOnly] public ComponentLookup<GpuEcsAnimatorStateComponent> AnimatorStateLookup; // Used for component checking only
            [Unity.Collections.ReadOnly] public ComponentLookup<GpuEcsAnimationDataComponent> AnimationDataLookup;
            [Unity.Collections.ReadOnly] public ComponentLookup<DeadTag> DeadTagLookup; // To check if entity is dead
            [Unity.Collections.ReadOnly] public ComponentLookup<HitTag> HitTagLookup;
            [Unity.Collections.ReadOnly] public ComponentLookup<HitAnimationStartedTag> HitAnimationStartedTagLookup;

            // Time information
            public float DeltaTime;
            public float ElapsedTime;

            // Logging control
            public bool ShouldLog;

            // Execute for each entity with the required components
            public void Execute(
                Entity entity,
                ref CharacterMovementState movementState,
                in CharacterSettings settings,
                in CharacterAnimatorReference animatorRef,
                in AgentBody agentBody,
                in LocalTransform transform,
                in EnemyTag enemyTag)
            {
                // Prevent overriding hit animation
                if (HitTagLookup.HasComponent(entity) || HitAnimationStartedTagLookup.HasComponent(entity))
                {
                    if (ShouldLog)
                    {
                        DebugLogManager.Instance.Log($"[EnemyAnimationSystem] Entity {entity.Index} has HitTag or HitAnimationStartedTag - skipping animation update", DebugLogSettings.LogType.EnemyAnimation);
                    }
                    return;
                }
                // Skip if animator entity is invalid
                if (animatorRef.AnimatorEntity == Entity.Null)
                {
                    if (ShouldLog)
                    {
                        Debug.LogWarning($"[EnemyAnimationSystem] Entity {entity.Index} has null animator entity");
                    }
                    return;
                }

                // Check if animator has required components
                if (!AnimatorControlLookup.HasComponent(animatorRef.AnimatorEntity))
                {
                    if (ShouldLog)
                    {
                        Debug.LogError($"[EnemyAnimationSystem] Animator entity {animatorRef.AnimatorEntity.Index} missing GpuEcsAnimatorControlComponent");
                    }
                    return;
                }

                if (!AnimatorStateLookup.HasComponent(animatorRef.AnimatorEntity))
                {
                    if (ShouldLog)
                    {
                        Debug.LogError($"[EnemyAnimationSystem] Animator entity {animatorRef.AnimatorEntity.Index} missing GpuEcsAnimatorStateComponent");
                    }
                    return;
                }

                if (!AnimationDataLookup.HasComponent(animatorRef.AnimatorEntity))
                {
                    if (ShouldLog)
                    {
                        Debug.LogError($"[EnemyAnimationSystem] Animator entity {animatorRef.AnimatorEntity.Index} missing GpuEcsAnimationDataComponent");
                    }
                    return;
                }

                // Get the current velocity from the agent body
                float3 velocity = agentBody.Velocity;
                float speed = math.length(velocity);

                // Determine if the character is moving
                bool isMoving = speed > 0.1f;

                // Skip dead entities - they are handled separately in the main OnUpdate method
                if (DeadTagLookup.HasComponent(entity))
                {
                    return;
                }

                // Also check if the entity has the Dead animation ID - if so, don't change it
                if (movementState.CurrentAnimationID == (int)EnemyAnimationIDs.Dead)
                {
                    return;
                }

                // Determine the appropriate animation ID based on movement state
                int targetAnimationID;
                string animationName = "Unknown";

                if (movementState.IsAttacking)
                {
                    // If attacking, play attack animation
                    targetAnimationID = (int)EnemyAnimationIDs.Attack;
                    animationName = "Attack";
                }
                else if (isMoving)
                {
                    // If moving, determine whether to walk or run based on speed
                    if (speed > settings.RunThreshold)
                    {
                        targetAnimationID = (int)EnemyAnimationIDs.Hit;
                        animationName = "Run";
                    }
                    else
                    {
                        targetAnimationID = (int)EnemyAnimationIDs.Walk;
                        animationName = "Walk";
                    }
                }
                else
                {
                    // If not moving, play idle animation
                    targetAnimationID = (int)EnemyAnimationIDs.Idle;
                    animationName = "Idle";
                }

                // Update the movement state
                movementState.Speed = speed;
                movementState.IsMoving = isMoving;
                movementState.Direction = isMoving ? math.normalize(velocity) : movementState.Direction;

                // Get the animation data
                var animationData = AnimationDataLookup[animatorRef.AnimatorEntity];

                // If the animation needs to change, update the animator
                if (targetAnimationID != movementState.CurrentAnimationID)
                {
                    // Get the animator control component
                    if (AnimatorControlLookup.TryGetComponent(animatorRef.AnimatorEntity, out var animatorControl))
                    {
                        // Check if the target animation ID is valid
                        if (targetAnimationID >= 0)
                        {
                            // Log the animation change
                            if (ShouldLog)
                            {
                                DebugLogManager.Instance.Log($"[EnemyAnimationSystem] Entity {entity.Index} changing animation from {movementState.CurrentAnimationID} to {targetAnimationID} ({animationName})", DebugLogSettings.LogType.EnemyAnimation);
                                DebugLogManager.Instance.Log($"[EnemyAnimationSystem] Speed: {speed}, IsMoving: {isMoving}, IsAttacking: {movementState.IsAttacking}", DebugLogSettings.LogType.EnemyAnimation);
                                DebugLogManager.Instance.Log($"[EnemyAnimationSystem] Control state: {animatorControl.animatorInfo.animationID}", DebugLogSettings.LogType.EnemyAnimation);
                            }

                            // Update the animator control component
                            animatorControl.animatorInfo.animationID = targetAnimationID;
                            animatorControl.transitionSpeed = settings.AnimationTransitionSpeed;

                            // Update the component
                            AnimatorControlLookup[animatorRef.AnimatorEntity] = animatorControl;

                            // Update the current animation ID
                            movementState.CurrentAnimationID = targetAnimationID;
                        }
                        else
                        {
                            if (ShouldLog)
                            {
                                Debug.LogError($"[EnemyAnimationSystem] Invalid animation ID {targetAnimationID}");
                            }
                        }
                    }
                    else
                    {
                        if (ShouldLog)
                        {
                            Debug.LogError($"[EnemyAnimationSystem] Failed to get animator control component for entity {animatorRef.AnimatorEntity.Index}");
                        }
                    }
                }
                else if (ShouldLog && movementState.IsAttacking)
                {
                    // Log current state for attacking enemies
                    DebugLogManager.Instance.Log($"[EnemyAnimationSystem] Entity {entity.Index} is attacking with animation ID {movementState.CurrentAnimationID}", DebugLogSettings.LogType.EnemyAnimation);
                    DebugLogManager.Instance.Log($"[EnemyAnimationSystem] Control state: {AnimatorControlLookup[animatorRef.AnimatorEntity].animatorInfo.animationID}", DebugLogSettings.LogType.EnemyAnimation);
                }
            }
        }
    }
}
