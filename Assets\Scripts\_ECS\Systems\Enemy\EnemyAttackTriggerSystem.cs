using GPUAnimationCrowds;
using PlayerFAP.Components;
using PlayerFAP.Components.Player;
using Unity.Burst;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using UnityEngine;

namespace Systems.PlayerFAP
{
    // This system randomly triggers attack animations for enemies to test animation transitions
    [UpdateBefore(typeof(EnemyAnimationSystem))]
    public partial struct EnemyAttackTriggerSystem : ISystem
    {
        // Random number generator
        private Unity.Mathematics.Random m_Random;
        
        // Timer for attack checks
        private float m_AttackCheckTimer;
        
        // Interval between attack checks
        private const float ATTACK_CHECK_INTERVAL = 1.0f;
        
        // Chance of attacking during a check (0-1)
        private const float ATTACK_CHANCE = 0.1f;
        
        // Duration of attack animation
        private const float ATTACK_DURATION = 1.5f;
        
        // For debugging
        private float m_LogTimer;
        private const float LOG_INTERVAL = 5.0f;
        
        public void OnCreate(ref SystemState state)
        {
            // Initialize random with a seed
            m_Random = Unity.Mathematics.Random.CreateFromIndex(1234);
            
            // Initialize timers
            m_AttackCheckTimer = 0;
            m_LogTimer = 0;
            
            // Require components for the system to run
            state.RequireForUpdate<CharacterMovementState>();
            state.RequireForUpdate<EnemyTag>();
            
            DebugLogManager.Instance.Log("[EnemyAttackTriggerSystem] System created");
        }
        
        public void OnUpdate(ref SystemState state)
        {
            // Get player position (assume only one player)
            float3 playerPosition = float3.zero;
            bool hasPlayer = false;
            foreach (var player in SystemAPI.Query<RefRO<PlayerHeadTransformComponent>>())
            {
                playerPosition = player.ValueRO.Position;
                hasPlayer = true;
                break;
            }
            if (!hasPlayer)
                return;

            // Update the timers
            m_AttackCheckTimer += state.WorldUnmanaged.Time.DeltaTime;
            m_LogTimer += state.WorldUnmanaged.Time.DeltaTime;
            
            // Log status periodically
            if (m_LogTimer >= LOG_INTERVAL)
            {
                m_LogTimer = 0;
                
                // Count attacking enemies
                int totalEnemies = 0;
                int attackingEnemies = 0;
                
                foreach (var (movementState, entity) in SystemAPI.Query<RefRO<CharacterMovementState>>().WithEntityAccess().WithAll<EnemyTag>())
                {
                    if (SystemAPI.HasComponent<HitTag>(entity))
                    {
                        // Don't overwrite the hit animation; let HitAnimationSystem handle it
                        continue;
                    }
                    
                    totalEnemies++;
                    if (movementState.ValueRO.IsAttacking)
                    {
                        attackingEnemies++;
                    }
                }
                
                //DebugLogManager.Instance.Log($"[EnemyAttackTriggerSystem] Total enemies: {totalEnemies}, Attacking: {attackingEnemies}");
            }
            
            // If it's time to check for attacks
            if (m_AttackCheckTimer >= ATTACK_CHECK_INTERVAL)
            {
                // Reset the timer
                m_AttackCheckTimer = 0;
                
                // Create a new job to handle attacks
                var attackJob = new AttackJob
                {
                    PlayerPosition = playerPosition,
                    Random = m_Random,
                    AttackChance = ATTACK_CHANCE,
                    AttackDuration = ATTACK_DURATION,
                    ElapsedTime = (float)state.WorldUnmanaged.Time.ElapsedTime,
                    DeltaTime = state.WorldUnmanaged.Time.DeltaTime,
                    ShouldLog = m_LogTimer < 0.1f // Log right after the periodic log
                };
                
                // Run the job without burst to allow for logging
                attackJob.Run();
                
                // Update the random number generator
                m_Random = attackJob.Random;
                
                //DebugLogManager.Instance.Log($"[EnemyAttackTriggerSystem] Attack check performed");
            }
        }
        
        // Job to handle character attacks
        [BurstCompile]
        private partial struct AttackJob : IJobEntity
        {
            public float3 PlayerPosition;
        
            // Random number generator
            public Unity.Mathematics.Random Random;
            
            // Chance of attacking
            public float AttackChance;
            
            // Duration of attack animation
            public float AttackDuration;
            
            // Current elapsed time
            public float ElapsedTime;
            
            // Delta time for this frame
            public float DeltaTime;
            
            // Whether to log details
            public bool ShouldLog;
            
            // Execute for each entity with the required components
            public void Execute(Entity entity, ref CharacterMovementState movementState, in LocalTransform enemyTransform, in EnemyStoppingDistance stoppingDistance)
            {
                // Calculate distance to player
                float distToPlayer = math.distance(enemyTransform.Position, PlayerPosition);

                // If already attacking, check if attack is finished
                if (movementState.IsAttacking)
                {
                    // Get a deterministic value based on entity index that serves as attack start time
                    float attackStartTime = entity.Index * 1000 % 10000 / 1000.0f;
                    
                    // If attack duration has passed, end the attack
                    if (ElapsedTime - attackStartTime > AttackDuration)
                    {
                        movementState.IsAttacking = false;
                        
                        if (ShouldLog)
                        {
                            //DebugLogManager.Instance.Log($"[EnemyAttackTriggerSystem] Entity {entity.Index} finished attacking");
                        }
                    }
                }
                // If not attacking, randomly start an attack
                else
                {
                    // Only attack if within stopping distance
                    if (distToPlayer <= stoppingDistance.Value)
                    {
                        movementState.IsAttacking = true;
                        if (ShouldLog)
                        {
                            //DebugLogManager.Instance.Log($"[EnemyAttackTriggerSystem] Entity {entity.Index} started attacking (distance {distToPlayer} <= stopping {stoppingDistance.Value})");
                        }
                    }
                }
            }
        }
    }
}
