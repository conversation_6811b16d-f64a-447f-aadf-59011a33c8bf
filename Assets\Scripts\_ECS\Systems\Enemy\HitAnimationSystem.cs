using GPUAnimationCrowds;
using GPUECSAnimationBaker.Engine.AnimatorSystem;
using PlayerFAP.Authorings;
using PlayerFAP.Components;
using PlayerFAP.Systems.Health;
using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;

namespace Systems.PlayerFAP
{
    // This system handles hit animations for enemies
    [UpdateAfter(typeof(DamageSystem))]
    [UpdateBefore(typeof(EnemyAnimationSystem))]
    public partial class HitAnimationSystem : SystemBase
    {
        private EntityCommandBufferSystem m_EndSimulationEcbSystem;

        protected override void OnCreate()
        {
            m_EndSimulationEcbSystem = World.GetOrCreateSystemManaged<EndSimulationEntityCommandBufferSystem>();
            RequireForUpdate<HitTag>();
        }

        protected override void OnUpdate()
        {
            var ecb = m_EndSimulationEcbSystem.CreateCommandBuffer();
            float deltaTime = SystemAPI.Time.DeltaTime;

            // Handle newly hit entities - process hit animation
            Entities
                .WithNone<HitAnimationStartedTag>()
                .WithAll<HitTag>()
                .ForEach((Entity entity,
                         ref CharacterMovementState movementState,
                         ref HitTag hitTag,
                         in CharacterAnimatorReference animatorRef) =>
                {
                    // Skip if animator entity is invalid
                    if (animatorRef.AnimatorEntity == Entity.Null)
                        return;

                    // Update the movement state to indicate this entity is hit
                    movementState.CurrentAnimationID = (int)EnemyAnimationIDs.Hit;
                    movementState.IsMoving = false;
                    movementState.IsAttacking = false;

                    // Add tag to mark that hit animation has started
                    ecb.AddComponent<HitAnimationStartedTag>(entity);
                    DebugLogManager.Instance.Log($"Entity {entity.Index} marked for hit animation with animation ID: {(int)EnemyAnimationIDs.Hit}", DebugLogSettings.LogType.EnemyAnimation);

                    // Force update of animator control component directly (explicit fix like DeathAnimationSystem)
                    if (SystemAPI.HasComponent<GpuEcsAnimatorControlComponent>(animatorRef.AnimatorEntity))
                    {
                        var animatorControl = SystemAPI.GetComponent<GpuEcsAnimatorControlComponent>(animatorRef.AnimatorEntity);
                        animatorControl.animatorInfo.animationID = (int)EnemyAnimationIDs.Hit;
                        animatorControl.animatorInfo.blendFactor = 0f;
                        animatorControl.animatorInfo.speedFactor = 1f;
                        animatorControl.transitionSpeed = 0.1f; // Quick transition to hit animation
                        SystemAPI.SetComponent(animatorRef.AnimatorEntity, animatorControl);
                        DebugLogManager.Instance.Log($"[HitAnimationSystem] Forced hit animation (ID: {(int)EnemyAnimationIDs.Hit}) on animator entity {animatorRef.AnimatorEntity.Index} for entity {entity.Index}", DebugLogSettings.LogType.EnemyAnimation);
                    }
                    else
                    {
                        DebugLogManager.Instance.LogWarning($"[HitAnimationSystem] Animator entity {animatorRef.AnimatorEntity.Index} missing GpuEcsAnimatorControlComponent for entity {entity.Index}");
                    }
                }).WithoutBurst().Run();

            // Update hit timers and remove hit tag when animation is complete
            Entities
                .WithAll<HitTag, HitAnimationStartedTag>()
                .ForEach((Entity entity, ref HitTag hitTag) =>
                {
                    // Update elapsed time
                    hitTag.HitTime += deltaTime;

                    // If hit animation is complete, remove the tag
                    if (hitTag.HitTime >= hitTag.HitAnimationDuration)
                    {
                        ecb.RemoveComponent<HitTag>(entity);
                        ecb.RemoveComponent<HitAnimationStartedTag>(entity);
                        DebugLogManager.Instance.Log($"Entity {entity.Index} hit animation complete after {hitTag.HitTime:F2} seconds", DebugLogSettings.LogType.EnemyAnimation);
                    }
                }).WithoutBurst().Run();

            m_EndSimulationEcbSystem.AddJobHandleForProducer(Dependency);
        }
    }

    // Tag to mark that hit animation has started
    public struct HitAnimationStartedTag : IComponentData
    {
    }
}
